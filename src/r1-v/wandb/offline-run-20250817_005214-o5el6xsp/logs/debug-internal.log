{"time":"2025-08-17T00:52:14.340738007+08:00","level":"INFO","msg":"stream: starting","core version":"0.21.0"}
{"time":"2025-08-17T00:52:14.475973483+08:00","level":"WARN","msg":"GraphQL client is nil, skipping feature loading"}
{"time":"2025-08-17T00:52:14.476334354+08:00","level":"INFO","msg":"stream: created new stream","id":"o5el6xsp"}
{"time":"2025-08-17T00:52:14.476385548+08:00","level":"INFO","msg":"stream: started","id":"o5el6xsp"}
{"time":"2025-08-17T00:52:14.476475067+08:00","level":"INFO","msg":"writer: Do: started","stream_id":"o5el6xsp"}
{"time":"2025-08-17T00:52:14.476508909+08:00","level":"INFO","msg":"handler: started","stream_id":"o5el6xsp"}
{"time":"2025-08-17T00:52:14.476561609+08:00","level":"INFO","msg":"sender: started","stream_id":"o5el6xsp"}
{"time":"2025-08-17T00:52:14.477807791+08:00","level":"WARN","msg":"runupserter: server does not expand metric globs but the x_server_side_expand_glob_metrics setting is set; ignoring"}
