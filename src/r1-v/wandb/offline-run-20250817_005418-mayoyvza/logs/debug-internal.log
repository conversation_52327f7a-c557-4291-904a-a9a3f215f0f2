{"time":"2025-08-17T00:54:18.765271474+08:00","level":"INFO","msg":"stream: starting","core version":"0.21.0"}
{"time":"2025-08-17T00:54:18.893913792+08:00","level":"WARN","msg":"GraphQL client is nil, skipping feature loading"}
{"time":"2025-08-17T00:54:18.894228824+08:00","level":"INFO","msg":"stream: created new stream","id":"mayoyvza"}
{"time":"2025-08-17T00:54:18.894273312+08:00","level":"INFO","msg":"stream: started","id":"mayoyvza"}
{"time":"2025-08-17T00:54:18.894376651+08:00","level":"INFO","msg":"writer: Do: started","stream_id":"mayoyvza"}
{"time":"2025-08-17T00:54:18.894431948+08:00","level":"INFO","msg":"sender: started","stream_id":"mayoyvza"}
{"time":"2025-08-17T00:54:18.894517264+08:00","level":"INFO","msg":"handler: started","stream_id":"mayoyvza"}
{"time":"2025-08-17T00:54:18.895673296+08:00","level":"WARN","msg":"runupserter: server does not expand metric globs but the x_server_side_expand_glob_metrics setting is set; ignoring"}
