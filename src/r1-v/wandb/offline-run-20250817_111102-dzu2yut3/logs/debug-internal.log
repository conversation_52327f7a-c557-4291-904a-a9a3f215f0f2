{"time":"2025-08-17T11:11:03.002309789+08:00","level":"INFO","msg":"stream: starting","core version":"0.21.0"}
{"time":"2025-08-17T11:11:03.131862705+08:00","level":"WARN","msg":"GraphQL client is nil, skipping feature loading"}
{"time":"2025-08-17T11:11:03.132143185+08:00","level":"INFO","msg":"stream: created new stream","id":"dzu2yut3"}
{"time":"2025-08-17T11:11:03.132180176+08:00","level":"INFO","msg":"stream: started","id":"dzu2yut3"}
{"time":"2025-08-17T11:11:03.132275322+08:00","level":"INFO","msg":"writer: Do: started","stream_id":"dzu2yut3"}
{"time":"2025-08-17T11:11:03.132354259+08:00","level":"INFO","msg":"sender: started","stream_id":"dzu2yut3"}
{"time":"2025-08-17T11:11:03.132434365+08:00","level":"INFO","msg":"handler: started","stream_id":"dzu2yut3"}
{"time":"2025-08-17T11:11:03.133095604+08:00","level":"WARN","msg":"runupserter: server does not expand metric globs but the x_server_side_expand_glob_metrics setting is set; ignoring"}
{"time":"2025-08-17T11:18:18.70260453+08:00","level":"INFO","msg":"stream: closing","id":"dzu2yut3"}
{"time":"2025-08-17T11:18:18.702926416+08:00","level":"INFO","msg":"handler: closed","stream_id":"dzu2yut3"}
{"time":"2025-08-17T11:18:18.702955878+08:00","level":"INFO","msg":"writer: Close: closed","stream_id":"dzu2yut3"}
{"time":"2025-08-17T11:18:18.702960075+08:00","level":"INFO","msg":"sender: closed","stream_id":"dzu2yut3"}
{"time":"2025-08-17T11:18:18.703076175+08:00","level":"INFO","msg":"stream: closed","id":"dzu2yut3"}
