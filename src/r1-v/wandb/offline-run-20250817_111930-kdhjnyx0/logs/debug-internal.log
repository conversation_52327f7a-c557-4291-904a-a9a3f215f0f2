{"time":"2025-08-17T11:19:30.861804162+08:00","level":"INFO","msg":"stream: starting","core version":"0.21.0"}
{"time":"2025-08-17T11:19:31.004060139+08:00","level":"WARN","msg":"GraphQL client is nil, skipping feature loading"}
{"time":"2025-08-17T11:19:31.004346151+08:00","level":"INFO","msg":"stream: created new stream","id":"kdhjnyx0"}
{"time":"2025-08-17T11:19:31.004390347+08:00","level":"INFO","msg":"stream: started","id":"kdhjnyx0"}
{"time":"2025-08-17T11:19:31.00442433+08:00","level":"INFO","msg":"writer: Do: started","stream_id":"kdhjnyx0"}
{"time":"2025-08-17T11:19:31.004496286+08:00","level":"INFO","msg":"sender: started","stream_id":"kdhjnyx0"}
{"time":"2025-08-17T11:19:31.00450501+08:00","level":"INFO","msg":"handler: started","stream_id":"kdhjnyx0"}
{"time":"2025-08-17T11:19:31.005359147+08:00","level":"WARN","msg":"runupserter: server does not expand metric globs but the x_server_side_expand_glob_metrics setting is set; ignoring"}
{"time":"2025-08-19T07:22:58.710983163+08:00","level":"INFO","msg":"stream: closing","id":"kdhjnyx0"}
{"time":"2025-08-19T07:22:58.711444923+08:00","level":"INFO","msg":"handler: closed","stream_id":"kdhjnyx0"}
{"time":"2025-08-19T07:22:58.71150255+08:00","level":"INFO","msg":"sender: closed","stream_id":"kdhjnyx0"}
{"time":"2025-08-19T07:22:58.711489976+08:00","level":"INFO","msg":"writer: Close: closed","stream_id":"kdhjnyx0"}
{"time":"2025-08-19T07:22:58.711589778+08:00","level":"INFO","msg":"stream: closed","id":"kdhjnyx0"}
