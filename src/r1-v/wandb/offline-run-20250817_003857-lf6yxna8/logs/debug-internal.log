{"time":"2025-08-17T00:38:57.833946435+08:00","level":"INFO","msg":"stream: starting","core version":"0.21.0"}
{"time":"2025-08-17T00:38:57.950242081+08:00","level":"WARN","msg":"GraphQL client is nil, skipping feature loading"}
{"time":"2025-08-17T00:38:57.95042762+08:00","level":"INFO","msg":"stream: created new stream","id":"lf6yxna8"}
{"time":"2025-08-17T00:38:57.95045571+08:00","level":"INFO","msg":"stream: started","id":"lf6yxna8"}
{"time":"2025-08-17T00:38:57.950477751+08:00","level":"INFO","msg":"writer: Do: started","stream_id":"lf6yxna8"}
{"time":"2025-08-17T00:38:57.950568596+08:00","level":"INFO","msg":"sender: started","stream_id":"lf6yxna8"}
{"time":"2025-08-17T00:38:57.950665885+08:00","level":"INFO","msg":"handler: started","stream_id":"lf6yxna8"}
{"time":"2025-08-17T00:38:58.014234832+08:00","level":"WARN","msg":"runupserter: server does not expand metric globs but the x_server_side_expand_glob_metrics setting is set; ignoring"}
