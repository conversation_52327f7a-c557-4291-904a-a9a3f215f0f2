{"time":"2025-08-17T00:45:10.89721633+08:00","level":"INFO","msg":"stream: starting","core version":"0.21.0"}
{"time":"2025-08-17T00:45:11.033296545+08:00","level":"WARN","msg":"GraphQL client is nil, skipping feature loading"}
{"time":"2025-08-17T00:45:11.033612441+08:00","level":"INFO","msg":"stream: created new stream","id":"c6axva44"}
{"time":"2025-08-17T00:45:11.033657643+08:00","level":"INFO","msg":"stream: started","id":"c6axva44"}
{"time":"2025-08-17T00:45:11.033688323+08:00","level":"INFO","msg":"writer: Do: started","stream_id":"c6axva44"}
{"time":"2025-08-17T00:45:11.033775216+08:00","level":"INFO","msg":"sender: started","stream_id":"c6axva44"}
{"time":"2025-08-17T00:45:11.033847782+08:00","level":"INFO","msg":"handler: started","stream_id":"c6axva44"}
{"time":"2025-08-17T00:45:11.034933369+08:00","level":"WARN","msg":"runupserter: server does not expand metric globs but the x_server_side_expand_glob_metrics setting is set; ignoring"}
