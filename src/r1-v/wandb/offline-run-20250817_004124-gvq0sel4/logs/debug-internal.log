{"time":"2025-08-17T00:41:24.918898409+08:00","level":"INFO","msg":"stream: starting","core version":"0.21.0"}
{"time":"2025-08-17T00:41:25.048714346+08:00","level":"WARN","msg":"GraphQL client is nil, skipping feature loading"}
{"time":"2025-08-17T00:41:25.049013243+08:00","level":"INFO","msg":"stream: created new stream","id":"gvq0sel4"}
{"time":"2025-08-17T00:41:25.049055385+08:00","level":"INFO","msg":"stream: started","id":"gvq0sel4"}
{"time":"2025-08-17T00:41:25.049144407+08:00","level":"INFO","msg":"writer: Do: started","stream_id":"gvq0sel4"}
{"time":"2025-08-17T00:41:25.049191787+08:00","level":"INFO","msg":"sender: started","stream_id":"gvq0sel4"}
{"time":"2025-08-17T00:41:25.049245292+08:00","level":"INFO","msg":"handler: started","stream_id":"gvq0sel4"}
{"time":"2025-08-17T00:41:25.049876273+08:00","level":"WARN","msg":"runupserter: server does not expand metric globs but the x_server_side_expand_glob_metrics setting is set; ignoring"}
